hint=com.coremedia.iso.boxes.TrackReferenceTypeBox(type)
cdsc=com.coremedia.iso.boxes.TrackReferenceTypeBox(type)
meta-ilst=com.coremedia.iso.boxes.apple.AppleItemListBox
rmra=com.coremedia.iso.boxes.apple.AppleReferenceMovieBox
rmda=com.coremedia.iso.boxes.apple.********************************
rmdr=com.coremedia.iso.boxes.apple.AppleDataRateBox
rdrf=com.coremedia.iso.boxes.apple.AppleDataReferenceBox

wave=com.coremedia.iso.boxes.apple.AppleWaveBox

udta-ccid=com.coremedia.iso.boxes.odf.OmaDrmContentIdBox
udta-yrrc=com.coremedia.iso.boxes.RecordingYearBox
udta-titl=com.coremedia.iso.boxes.TitleBox
udta-dscp=com.coremedia.iso.boxes.DescriptionBox
udta-icnu=com.coremedia.iso.boxes.odf.OmaDrmIconUriBox
udta-infu=com.coremedia.iso.boxes.odf.OmaDrmInfoUrlBox
udta-albm=com.coremedia.iso.boxes.AlbumBox
udta-cprt=com.coremedia.iso.boxes.CopyrightBox
udta-gnre=com.coremedia.iso.boxes.GenreBox
udta-perf=com.coremedia.iso.boxes.PerformerBox
udta-auth=com.coremedia.iso.boxes.AuthorBox
udta-kywd=com.coremedia.iso.boxes.KeywordsBox
udta-loci=com.coremedia.iso.boxes.threegpp26244.LocationInformationBox
udta-rtng=com.coremedia.iso.boxes.RatingBox
udta-clsf=com.coremedia.iso.boxes.ClassificationBox
udta-cdis=com.coremedia.iso.boxes.vodafone.ContentDistributorIdBox
udta-albr=com.coremedia.iso.boxes.vodafone.AlbumArtistBox
udta-cvru=com.coremedia.iso.boxes.odf.OmaDrmCoverUriBox
udta-lrcu=com.coremedia.iso.boxes.odf.OmaDrmLyricsUriBox




tx3g=com.coremedia.iso.boxes.sampleentry.TextSampleEntry
stsd-text=com.googlecode.mp4parser.boxes.apple.QuicktimeTextSampleEntry
enct=com.coremedia.iso.boxes.sampleentry.TextSampleEntry(type)
samr=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
sawb=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
mp4a=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
drms=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-alac=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
mp4s=com.coremedia.iso.boxes.sampleentry.MpegSampleEntry(type)
owma=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
ac-3=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
dac3=com.googlecode.mp4parser.boxes.AC3SpecificBox
ec-3=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
dec3=com.googlecode.mp4parser.boxes.EC3SpecificBox
stsd-lpcm=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsc=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsh=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-dtsl=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
ddts=com.googlecode.mp4parser.boxes.DTSSpecificBox
stsd-dtse=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
stsd-mlpa=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
dmlp=com.googlecode.mp4parser.boxes.MLPSpecificBox
enca=com.coremedia.iso.boxes.sampleentry.AudioSampleEntry(type)
encv=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
mp4v=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
s263=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
avc1=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
avc3=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
hev1=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
hvc1=com.coremedia.iso.boxes.sampleentry.VisualSampleEntry(type)
ovc1=com.coremedia.iso.boxes.sampleentry.Ovc1VisualSampleEntryImpl
stpp=com.mp4parser.iso14496.part30.XMLSubtitleSampleEntry
avcC=com.mp4parser.iso14496.part15.AvcConfigurationBox
hvcC=com.mp4parser.iso14496.part15.HevcConfigurationBox
alac=com.coremedia.iso.boxes.apple.AppleLosslessSpecificBox
btrt=com.mp4parser.iso14496.part12.BitRateBox
ftyp=com.coremedia.iso.boxes.FileTypeBox
mdat=com.coremedia.iso.boxes.mdat.MediaDataBox
moov=com.coremedia.iso.boxes.MovieBox
mvhd=com.coremedia.iso.boxes.MovieHeaderBox
trak=com.coremedia.iso.boxes.TrackBox
tkhd=com.coremedia.iso.boxes.TrackHeaderBox
edts=com.coremedia.iso.boxes.EditBox
elst=com.coremedia.iso.boxes.EditListBox
mdia=com.coremedia.iso.boxes.MediaBox
mdhd=com.coremedia.iso.boxes.MediaHeaderBox
hdlr=com.coremedia.iso.boxes.HandlerBox
minf=com.coremedia.iso.boxes.MediaInformationBox
vmhd=com.coremedia.iso.boxes.VideoMediaHeaderBox
smhd=com.coremedia.iso.boxes.SoundMediaHeaderBox
sthd=com.coremedia.iso.boxes.SubtitleMediaHeaderBox
hmhd=com.coremedia.iso.boxes.HintMediaHeaderBox
dinf=com.coremedia.iso.boxes.DataInformationBox
dref=com.coremedia.iso.boxes.DataReferenceBox
url\ =com.coremedia.iso.boxes.DataEntryUrlBox
urn\ =com.coremedia.iso.boxes.DataEntryUrnBox
stbl=com.coremedia.iso.boxes.SampleTableBox
ctts=com.coremedia.iso.boxes.CompositionTimeToSample
stsd=com.coremedia.iso.boxes.SampleDescriptionBox
stts=com.coremedia.iso.boxes.TimeToSampleBox
stss=com.coremedia.iso.boxes.SyncSampleBox
stsc=com.coremedia.iso.boxes.SampleToChunkBox
stsz=com.coremedia.iso.boxes.SampleSizeBox
stco=com.coremedia.iso.boxes.StaticChunkOffsetBox
subs=com.coremedia.iso.boxes.SubSampleInformationBox
udta=com.coremedia.iso.boxes.UserDataBox
skip=com.coremedia.iso.boxes.FreeSpaceBox
tref=com.coremedia.iso.boxes.TrackReferenceBox
iloc=com.coremedia.iso.boxes.ItemLocationBox
idat=com.coremedia.iso.boxes.ItemDataBox

damr=com.coremedia.iso.boxes.sampleentry.AmrSpecificBox
meta=com.coremedia.iso.boxes.MetaBox
ipro=com.coremedia.iso.boxes.ItemProtectionBox
sinf=com.coremedia.iso.boxes.ProtectionSchemeInformationBox
frma=com.coremedia.iso.boxes.OriginalFormatBox
schi=com.coremedia.iso.boxes.SchemeInformationBox
odkm=com.coremedia.iso.boxes.odf.OmaDrmKeyManagenentSystemBox
odaf=com.coremedia.iso.boxes.OmaDrmAccessUnitFormatBox
schm=com.coremedia.iso.boxes.SchemeTypeBox
uuid=com.coremedia.iso.boxes.UserBox(userType)
free=com.coremedia.iso.boxes.FreeBox
styp=com.coremedia.iso.boxes.fragment.SegmentTypeBox
mvex=com.coremedia.iso.boxes.fragment.MovieExtendsBox
mehd=com.coremedia.iso.boxes.fragment.MovieExtendsHeaderBox
trex=com.coremedia.iso.boxes.fragment.TrackExtendsBox

moof=com.coremedia.iso.boxes.fragment.MovieFragmentBox
mfhd=com.coremedia.iso.boxes.fragment.MovieFragmentHeaderBox
traf=com.coremedia.iso.boxes.fragment.TrackFragmentBox
tfhd=com.coremedia.iso.boxes.fragment.TrackFragmentHeaderBox
trun=com.coremedia.iso.boxes.fragment.TrackRunBox
sdtp=com.coremedia.iso.boxes.SampleDependencyTypeBox
mfra=com.coremedia.iso.boxes.fragment.MovieFragmentRandomAccessBox
tfra=com.coremedia.iso.boxes.fragment.TrackFragmentRandomAccessBox
mfro=com.coremedia.iso.boxes.fragment.MovieFragmentRandomAccessOffsetBox
tfdt=com.coremedia.iso.boxes.fragment.TrackFragmentBaseMediaDecodeTimeBox
nmhd=com.coremedia.iso.boxes.NullMediaHeaderBox
gmhd=com.googlecode.mp4parser.boxes.apple.GenericMediaHeaderAtom
gmhd-text=com.googlecode.mp4parser.boxes.apple.GenericMediaHeaderTextAtom
gmin=com.googlecode.mp4parser.boxes.apple.BaseMediaInfoAtom
cslg=com.coremedia.iso.boxes.CompositionShiftLeastGreatestAtom
pdin=com.coremedia.iso.boxes.ProgressiveDownloadInformationBox
bloc=com.googlecode.mp4parser.boxes.dece.BaseLocationBox
ftab=com.googlecode.mp4parser.boxes.threegpp26245.FontTableBox
co64=com.coremedia.iso.boxes.ChunkOffset64BitBox
xml\ =com.coremedia.iso.boxes.XmlBox
avcn=com.googlecode.mp4parser.boxes.basemediaformat.AvcNalUnitStorageBox
ainf=com.googlecode.mp4parser.boxes.dece.AssetInformationBox
pssh=com.mp4parser.iso23001.part7.ProtectionSystemSpecificHeaderBox
trik=com.coremedia.iso.boxes.dece.TrickPlayBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.PiffSampleEncryptionBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.PiffTrackEncryptionBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.TfrfBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.TfxdBox
uuid[********************************]=com.googlecode.mp4parser.boxes.piff.UuidBasedProtectionSystemSpecificHeaderBox
senc=com.googlecode.mp4parser.boxes.dece.SampleEncryptionBox
tenc=com.mp4parser.iso23001.part7.TrackEncryptionBox
amf0=com.googlecode.mp4parser.boxes.adobe.ActionMessageFormat0SampleEntryBox

#iods=com.googlecode.mp4parser.boxes.mp4.ObjectDescriptorBox
esds=com.googlecode.mp4parser.boxes.mp4.ESDescriptorBox

tmcd=com.googlecode.mp4parser.boxes.apple.TimeCodeBox
sidx=com.googlecode.mp4parser.boxes.threegpp26244.SegmentIndexBox

sbgp=com.googlecode.mp4parser.boxes.mp4.samplegrouping.SampleToGroupBox
sgpd=com.googlecode.mp4parser.boxes.mp4.samplegrouping.SampleGroupDescriptionBox

tapt=com.googlecode.mp4parser.boxes.apple.TrackApertureModeDimensionAtom
clef=com.googlecode.mp4parser.boxes.apple.CleanApertureAtom
prof=com.googlecode.mp4parser.boxes.apple.TrackProductionApertureDimensionsAtom
enof=com.googlecode.mp4parser.boxes.apple.TrackEncodedPixelsDimensionsAtom
pasp=com.googlecode.mp4parser.boxes.apple.PixelAspectRationAtom
load=com.googlecode.mp4parser.boxes.apple.TrackLoadSettingsAtom


default=com.coremedia.iso.boxes.UnknownBox(type)



#stsd-rtp\ =com.coremedia.iso.boxes.rtp.RtpHintSampleEntry(type)
#udta-hnti=com.coremedia.iso.boxes.rtp.HintInformationBox
#udta-hinf=com.coremedia.iso.boxes.rtp.HintStatisticsBox
#hnti-sdp\ =com.coremedia.iso.boxes.rtp.RtpTrackSdpHintInformationBox
#hnti-rtp\ =com.coremedia.iso.boxes.rtp.RtpMovieHintInformationBox
#hinf-pmax=com.coremedia.iso.boxes.rtp.LargestHintPacketBox
#hinf-payt=com.coremedia.iso.boxes.rtp.PayloadTypeBox
#hinf-tmin=com.coremedia.iso.boxes.rtp.SmallestRelativeTransmissionTimeBox
#hinf-tmax=com.coremedia.iso.boxes.rtp.LargestRelativeTransmissionTimeBox
#hinf-maxr=com.coremedia.iso.boxes.rtp.MaximumDataRateBox
#hinf-dmax=com.coremedia.iso.boxes.rtp.LargestHintPacketDurationBox
#hinf-hnti=com.coremedia.iso.boxes.rtp.HintInformationBox
#hinf-tims=com.coremedia.iso.boxes.rtp.TimeScaleEntry

#hinf-nump=com.coremedia.iso.boxes.rtp.HintPacketsSentBox(type)
#hinf-npck=com.coremedia.iso.boxes.rtp.HintPacketsSentBox(type)

#hinf-trpy=com.coremedia.iso.boxes.rtp.HintStatisticBoxes(type)
#hinf-totl=com.coremedia.iso.boxes.rtp.HintStatisticBoxes(type)
#hinf-tpyl=com.coremedia.iso.boxes.rtp.HintStatisticBoxes(type)
#hinf-tpay=com.coremedia.iso.boxes.rtp.HintStatisticBoxes(type)
#hinf-dmed=com.coremedia.iso.boxes.rtp.HintStatisticBoxes(type)
#hinf-dimm=com.coremedia.iso.boxes.rtp.HintStatisticBoxes(type)
#hinf-drep=com.coremedia.iso.boxes.rtp.HintStatisticBoxes(type)
#tims=com.coremedia.iso.boxes.rtp.TimeScaleEntry

#odrm=com.coremedia.iso.boxes.odf.OmaDrmContainerBox
#mdri=com.coremedia.iso.boxes.odf.MutableDrmInformationBox
#odtt=com.coremedia.iso.boxes.odf.OmaDrmTransactionTrackingBox
#odrb=com.coremedia.iso.boxes.odf.OmaDrmRightsObjectBox
#odhe=com.coremedia.iso.boxes.odf.OmaDrmDiscreteHeadersBox
#odda=com.coremedia.iso.boxes.odf.OmaDrmContentObjectBox
#ohdr=com.coremedia.iso.boxes.odf.OmaDrmCommonHeadersBox
#grpi=com.coremedia.iso.boxes.odf.OmaDrmGroupIdBox
\u00A9nam=com.googlecode.mp4parser.boxes.apple.AppleNameBox
\u00A9ART=com.googlecode.mp4parser.boxes.apple.AppleArtistBox
aART=com.googlecode.mp4parser.boxes.apple.AppleArtist2Box
\u00A9alb=com.googlecode.mp4parser.boxes.apple.AppleAlbumBox
\u00A9gen=com.googlecode.mp4parser.boxes.apple.AppleGenreBox
gnre=com.googlecode.mp4parser.boxes.apple.AppleGenreIDBox
#\u00A9day=com.googlecode.mp4parser.boxes.apple.AppleRecordingYearBox
\u00A9day=com.googlecode.mp4parser.boxes.apple.AppleRecordingYear2Box
trkn=com.googlecode.mp4parser.boxes.apple.AppleTrackNumberBox
cpil=com.googlecode.mp4parser.boxes.apple.AppleCompilationBox
pgap=com.googlecode.mp4parser.boxes.apple.AppleGaplessPlaybackBox
disk=com.googlecode.mp4parser.boxes.apple.AppleDiskNumberBox
apID=com.googlecode.mp4parser.boxes.apple.AppleAppleIdBox
cprt=com.googlecode.mp4parser.boxes.apple.AppleCopyrightBox
atID=com.googlecode.mp4parser.boxes.apple.Apple_atIDBox
geID=com.googlecode.mp4parser.boxes.apple.Apple_geIDBox
sfID=com.googlecode.mp4parser.boxes.apple.AppleCountryTypeBoxBox
desc=com.googlecode.mp4parser.boxes.apple.AppleDescriptionBox
tvnn=com.googlecode.mp4parser.boxes.apple.AppleTVNetworkBox
tvsh=com.googlecode.mp4parser.boxes.apple.AppleTVShowBox
tven=com.googlecode.mp4parser.boxes.apple.AppleTVEpisodeNumberBox
tvsn=com.googlecode.mp4parser.boxes.apple.AppleTVSeasonBox
tves=com.googlecode.mp4parser.boxes.apple.AppleTVEpisodeBox
xid\ =com.googlecode.mp4parser.boxes.apple.Apple_xid_Box
flvr=com.googlecode.mp4parser.boxes.apple.Apple_flvr_Box
sdes=com.googlecode.mp4parser.boxes.apple.AppleShortDescriptionBox
ldes=com.googlecode.mp4parser.boxes.apple.AppleLongDescriptionBox
soal=com.googlecode.mp4parser.boxes.apple.AppleSortAlbumBox
purd=com.googlecode.mp4parser.boxes.apple.ApplePurchaseDateBox
stik=com.googlecode.mp4parser.boxes.apple.AppleMediaTypeBox


#added by Tobias Bley / UltraMixer (04/25/2014)
\u00A9cmt=com.googlecode.mp4parser.boxes.apple.AppleCommentBox
tmpo=com.googlecode.mp4parser.boxes.apple.AppleTempoBox
\u00A9too=com.googlecode.mp4parser.boxes.apple.AppleEncoderBox
\u00A9wrt=com.googlecode.mp4parser.boxes.apple.AppleTrackAuthorBox
\u00A9grp=com.googlecode.mp4parser.boxes.apple.AppleGroupingBox
covr=com.googlecode.mp4parser.boxes.apple.AppleCoverBox
\u00A9lyr=com.googlecode.mp4parser.boxes.apple.AppleLyricsBox
cinf=com.googlecode.mp4parser.boxes.dece.ContentInformationBox
tibr=com.mp4parser.iso14496.part15.TierBitRateBox
tiri=com.mp4parser.iso14496.part15.TierInfoBox
svpr=com.mp4parser.iso14496.part15.PriotityRangeBox
emsg=com.mp4parser.iso23009.part1.EventMessageBox
saio=com.mp4parser.iso14496.part12.SampleAuxiliaryInformationOffsetsBox
saiz=com.mp4parser.iso14496.part12.SampleAuxiliaryInformationSizesBox
vttC=com.mp4parser.iso14496.part30.WebVTTConfigurationBox
vlab=com.mp4parser.iso14496.part30.WebVTTSourceLabelBox
wvtt=com.mp4parser.iso14496.part30.WebVTTSampleEntry


#added by marwatk (2/24/2014)
Xtra=com.googlecode.mp4parser.boxes.microsoft.XtraBox
\u00A9xyz=com.googlecode.mp4parser.boxes.apple.AppleGPSCoordinatesBox