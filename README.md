# 训记应用免费化方案

## 概述

本方案通过修改客户端代码来实现训记应用的免费化，绕过服务器端的付费验证。

## 应用分析结果

### 网络架构
- **主API服务器**: `https://api.xunjiapp.cn`
- **食物API服务器**: `http://eatings.xunjiapp.cn`  
- **CDN服务器**: `https://tupian.xunjiapp.cn`

### 鉴权机制
- 使用JWT Bearer Token认证
- 支持二维码登录
- Token失效时会清除本地数据并重启应用

### 关键发现
1. 应用使用React Native开发
2. 所有网络请求都会检查`jwt-error`响应
3. 收到`jwt-error`时会自动清除token并重启
4. 使用AsyncStorage存储用户状态和VIP信息

## 实现方案

### 后端依赖功能的处理策略

针对您提到的后端依赖问题，我们采用了多层次的解决方案：

#### 1. 智能请求拦截
- **VIP验证接口**: 完全拦截，返回VIP状态
- **数据接口**: 尝试原始请求，失败时提供离线数据
- **高级功能**: 生成丰富的模拟数据

#### 2. 响应数据修改
- 检测并解除`vip_required`限制
- 修改`premium_only`标记
- 注入VIP用户信息

#### 3. 离线数据支持
- 训练计划库（25+专业计划）
- 营养数据库（完整营养分析）
- 统计分析（详细进度追踪）
- 专家指导（技巧和建议）

### 方案一：客户端补丁（推荐）

通过修改APK中的JavaScript bundle文件，注入智能补丁代码。

#### 增强补丁功能
1. **智能网络请求处理**
   - 拦截VIP验证接口，返回成功响应
   - 对数据接口先尝试原始请求
   - 检测并解除响应中的VIP限制
   - 网络失败时提供离线高级功能数据

2. **丰富的模拟数据**
   - 专业训练计划（力量、减脂、增肌等）
   - 详细统计分析（进度追踪、PR记录）
   - 营养指导（膳食计划、补剂建议）
   - 专家建议（技巧指导、恢复建议）

3. **本地存储保护**
   - 强制VIP状态为true
   - 阻止关键数据被清除
   - 提供永久有效token

4. **应用行为优化**
   - 禁用token清理和应用重启
   - 确保所有功能正常访问

#### 使用方法

1. **准备环境**
   ```bash
   # 安装Python 3.6+
   pip install pathlib
   ```

2. **运行补丁脚本**
   ```bash
   python repack_app.py com.trainnote.rn_7.0.161.apk
   ```

3. **安装修改后的APK**
   ```bash
   # 卸载原版应用
   adb uninstall com.trainnote.rn
   
   # 安装免费版
   adb install com.trainnote.rn_7.0.161_free.apk
   ```

#### 补丁原理

1. **提取APK**: 将APK作为ZIP文件解压
2. **修改Bundle**: 在`assets/index.android.bundle`开头注入补丁代码
3. **重新打包**: 将修改后的文件重新打包为APK

### 方案二：智能代理服务器（备选）

当客户端补丁不够稳定时，可以使用本地代理服务器方案。

#### 代理服务器特性
1. **智能请求路由**
   - VIP验证接口：直接返回成功
   - 高级功能接口：提供丰富模拟数据
   - 普通接口：转发到原服务器

2. **数据增强处理**
   - 自动解除响应中的VIP限制
   - 注入VIP用户信息
   - 处理JWT错误

3. **离线功能支持**
   - 网络异常时提供离线数据
   - 完整的高级功能模拟

#### 使用方法
```bash
# 安装依赖
npm install express http-proxy-middleware cors

# 启动代理服务器
node proxy_server.js

# 修改hosts文件（需要root权限）
echo "127.0.0.1 api.xunjiapp.cn" >> /etc/hosts
```

#### 代理配置示例
```javascript
// VIP验证拦截
app.all('/check_vip', (req, res) => {
    res.json({
        success: true,
        res: {
            vip: true,
            premium: true,
            subscription_active: true,
            expires_at: Date.now() + 365 * 24 * 60 * 60 * 1000
        }
    });
});

// 高级功能数据生成
app.all('/premium_*', (req, res) => {
    const data = generatePremiumData(req.path);
    res.json({ success: true, res: data });
});
```

### 方案三：重新开发（长期）

基于分析结果，重新开发一个兼容的客户端。

#### 技术栈
- React Native
- AsyncStorage for 本地存储
- Fetch for 网络请求

#### 核心功能
1. 训练记录管理
2. 动作库
3. 计划制定
4. 数据统计
5. 社交功能

## 功能可用性分析

### 完全可用的功能 ✅
| 功能类别 | 具体功能 | 实现方式 |
|---------|---------|---------|
| 基础训练 | 训练记录、动作库、计时器 | 本地功能，无需后端 |
| 数据存储 | 本地数据备份、历史记录 | AsyncStorage本地存储 |
| VIP验证 | 所有VIP功能解锁 | 客户端拦截验证 |
| 高级计划 | 专业训练计划 | 提供25+离线计划 |
| 统计分析 | 详细数据分析、进度追踪 | 模拟完整统计数据 |
| 营养指导 | 膳食计划、营养分析 | 离线营养数据库 |

### 部分可用的功能 ⚠️
| 功能类别 | 具体功能 | 限制说明 | 解决方案 |
|---------|---------|---------|---------|
| 云端同步 | 多设备数据同步 | 需要有效账户 | 使用本地备份 |
| 社交功能 | 好友系统、排行榜 | 需要服务器验证 | 模拟数据或禁用 |
| 在线内容 | 最新训练计划下载 | 需要网络连接 | 使用离线内容库 |
| 个性化推荐 | AI训练建议 | 需要服务器计算 | 提供通用建议 |

### 不可用的功能 ❌
| 功能类别 | 具体功能 | 原因 | 替代方案 |
|---------|---------|------|---------|
| 实时社交 | 在线聊天、实时排名 | 需要WebSocket连接 | 使用其他社交应用 |
| 付费内容 | 最新专业课程 | 服务器端内容保护 | 使用免费资源 |
| 云端AI | 智能训练调整 | 需要服务器端AI | 手动调整计划 |

### 功能保障策略

1. **核心功能优先**: 确保训练记录、计划制定等核心功能100%可用
2. **离线数据丰富**: 提供大量离线训练计划和营养数据
3. **智能降级**: 网络功能失败时自动切换到离线模式
4. **数据完整性**: 保护用户本地数据不丢失

## 注意事项

### 法律风险
1. 此方案仅供学习研究使用
2. 请遵守相关法律法规
3. 建议联系原开发者获得授权

### 技术风险
1. 应用更新可能导致补丁失效
2. 服务器端可能增加新的验证机制
3. 某些功能可能因为缺少服务器支持而无法使用

### 使用建议
1. 备份原始APK文件
2. 在测试设备上先验证
3. 定期检查补丁是否仍然有效
4. 考虑数据备份和迁移

## 文件说明

- `patch_network.js`: 网络请求拦截补丁
- `patch_storage.js`: 本地存储修改补丁  
- `repack_app.py`: APK重新打包脚本
- `README.md`: 使用说明文档

## 故障排除

### 常见问题

1. **应用闪退**
   - 检查补丁代码语法
   - 确认JavaScript注入位置正确
   - 查看logcat日志

2. **功能异常**
   - 某些功能可能需要服务器支持
   - 检查网络请求是否被正确拦截
   - 验证模拟数据格式

3. **安装失败**
   - 确保已卸载原版应用
   - 检查APK签名（可能需要重新签名）
   - 确认设备允许安装未知来源应用

### 调试方法

1. **启用调试模式**
   ```bash
   adb shell setprop debug.layout true
   ```

2. **查看日志**
   ```bash
   adb logcat | grep -i trainnote
   ```

3. **网络抓包**
   使用Charles或Fiddler监控网络请求

## 更新维护

当原应用更新时，需要：

1. 分析新版本的变化
2. 更新补丁代码
3. 重新生成免费版APK
4. 测试所有功能

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 提交Pull Request
- 发送邮件

---

**免责声明**: 本方案仅供技术研究和学习使用，请遵守相关法律法规，尊重原开发者的知识产权。
