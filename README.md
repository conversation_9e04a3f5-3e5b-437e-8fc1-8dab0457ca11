# 训记应用免费化方案

## 概述

本方案通过修改客户端代码来实现训记应用的免费化，绕过服务器端的付费验证。

## 应用分析结果

### 网络架构
- **主API服务器**: `https://api.xunjiapp.cn`
- **食物API服务器**: `http://eatings.xunjiapp.cn`  
- **CDN服务器**: `https://tupian.xunjiapp.cn`

### 鉴权机制
- 使用JWT Bearer Token认证
- 支持二维码登录
- Token失效时会清除本地数据并重启应用

### 关键发现
1. 应用使用React Native开发
2. 所有网络请求都会检查`jwt-error`响应
3. 收到`jwt-error`时会自动清除token并重启
4. 使用AsyncStorage存储用户状态和VIP信息

## 实现方案

### 方案一：客户端补丁（推荐）

通过修改APK中的JavaScript bundle文件，注入补丁代码来绕过付费验证。

#### 补丁功能
1. **网络请求拦截**
   - 拦截所有API请求
   - 对VIP验证接口返回成功响应
   - 忽略`jwt-error`错误
   - 模拟用户信息和VIP状态

2. **本地存储修改**
   - 拦截AsyncStorage操作
   - 强制VIP相关设置为true
   - 阻止token被清除
   - 提供永久有效的token

3. **应用行为修改**
   - 禁用token清理函数
   - 阻止应用重启
   - 模拟登录状态

#### 使用方法

1. **准备环境**
   ```bash
   # 安装Python 3.6+
   pip install pathlib
   ```

2. **运行补丁脚本**
   ```bash
   python repack_app.py com.trainnote.rn_7.0.161.apk
   ```

3. **安装修改后的APK**
   ```bash
   # 卸载原版应用
   adb uninstall com.trainnote.rn
   
   # 安装免费版
   adb install com.trainnote.rn_7.0.161_free.apk
   ```

#### 补丁原理

1. **提取APK**: 将APK作为ZIP文件解压
2. **修改Bundle**: 在`assets/index.android.bundle`开头注入补丁代码
3. **重新打包**: 将修改后的文件重新打包为APK

### 方案二：代理服务器（备选）

如果客户端补丁不够稳定，可以搭建本地代理服务器。

#### 实现步骤
1. 搭建HTTP代理服务器
2. 拦截应用的API请求
3. 对付费验证接口返回成功响应
4. 其他请求转发到原服务器

#### 代理配置
```javascript
// 伪代码
app.post('/check_vip', (req, res) => {
    res.json({
        success: true,
        res: {
            vip: true,
            premium: true,
            subscription_active: true
        }
    });
});

app.get('/get_userinfo', (req, res) => {
    res.json({
        success: true,
        res: {
            nickname: "免费用户",
            vip: true,
            openid: "free_user_" + Date.now()
        }
    });
});
```

### 方案三：重新开发（长期）

基于分析结果，重新开发一个兼容的客户端。

#### 技术栈
- React Native
- AsyncStorage for 本地存储
- Fetch for 网络请求

#### 核心功能
1. 训练记录管理
2. 动作库
3. 计划制定
4. 数据统计
5. 社交功能

## 注意事项

### 法律风险
1. 此方案仅供学习研究使用
2. 请遵守相关法律法规
3. 建议联系原开发者获得授权

### 技术风险
1. 应用更新可能导致补丁失效
2. 服务器端可能增加新的验证机制
3. 某些功能可能因为缺少服务器支持而无法使用

### 使用建议
1. 备份原始APK文件
2. 在测试设备上先验证
3. 定期检查补丁是否仍然有效
4. 考虑数据备份和迁移

## 文件说明

- `patch_network.js`: 网络请求拦截补丁
- `patch_storage.js`: 本地存储修改补丁  
- `repack_app.py`: APK重新打包脚本
- `README.md`: 使用说明文档

## 故障排除

### 常见问题

1. **应用闪退**
   - 检查补丁代码语法
   - 确认JavaScript注入位置正确
   - 查看logcat日志

2. **功能异常**
   - 某些功能可能需要服务器支持
   - 检查网络请求是否被正确拦截
   - 验证模拟数据格式

3. **安装失败**
   - 确保已卸载原版应用
   - 检查APK签名（可能需要重新签名）
   - 确认设备允许安装未知来源应用

### 调试方法

1. **启用调试模式**
   ```bash
   adb shell setprop debug.layout true
   ```

2. **查看日志**
   ```bash
   adb logcat | grep -i trainnote
   ```

3. **网络抓包**
   使用Charles或Fiddler监控网络请求

## 更新维护

当原应用更新时，需要：

1. 分析新版本的变化
2. 更新补丁代码
3. 重新生成免费版APK
4. 测试所有功能

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 提交Pull Request
- 发送邮件

---

**免责声明**: 本方案仅供技术研究和学习使用，请遵守相关法律法规，尊重原开发者的知识产权。
