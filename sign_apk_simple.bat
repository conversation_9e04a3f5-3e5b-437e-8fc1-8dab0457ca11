@echo off
REM 简单的APK签名脚本
REM 使用uber-apk-signer工具

echo 正在为APK添加签名...

REM 检查输入参数
if "%1"=="" (
    echo 用法: sign_apk_simple.bat APK文件名
    echo 示例: sign_apk_simple.bat com.trainnote.rn_7.0.161_free.apk
    pause
    exit /b 1
)

set APK_FILE=%1
set SIGNED_APK=%~n1_signed.apk

REM 检查APK文件是否存在
if not exist "%APK_FILE%" (
    echo 错误: 找不到APK文件 %APK_FILE%
    pause
    exit /b 1
)

REM 检查是否有uber-apk-signer
where uber-apk-signer >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 找不到uber-apk-signer工具
    echo 请下载并安装: https://github.com/patrickfav/uber-apk-signer/releases
    echo 或者使用Java方式: java -jar uber-apk-signer.jar
    pause
    exit /b 1
)

REM 使用uber-apk-signer签名
echo 正在签名APK文件...
uber-apk-signer --apks "%APK_FILE%" --out "%SIGNED_APK%"

if %errorlevel% equ 0 (
    echo.
    echo ================================================
    echo APK签名成功!
    echo 签名后的文件: %SIGNED_APK%
    echo 现在可以安装了: adb install "%SIGNED_APK%"
    echo ================================================
) else (
    echo APK签名失败!
    pause
    exit /b 1
)

pause
