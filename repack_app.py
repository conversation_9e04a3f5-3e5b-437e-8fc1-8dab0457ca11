#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训记应用免费化重新打包脚本
用于修改APK文件，注入免费化补丁
"""

import os
import sys
import shutil
import subprocess
import tempfile
import zipfile
import re
from pathlib import Path

class TrainNoteFreePatcher:
    def __init__(self, apk_path):
        self.apk_path = Path(apk_path)
        self.work_dir = None
        self.patched_apk_path = None
        
    def extract_apk(self):
        """提取APK文件"""
        print("正在提取APK文件...")
        self.work_dir = Path(tempfile.mkdtemp(prefix="trainnote_patch_"))
        
        with zipfile.ZipFile(self.apk_path, 'r') as zip_ref:
            zip_ref.extractall(self.work_dir)
        
        print(f"APK已提取到: {self.work_dir}")
        return True
    
    def patch_bundle(self):
        """修改React Native bundle文件"""
        print("正在修补bundle文件...")
        
        bundle_path = self.work_dir / "assets" / "index.android.bundle"
        if not bundle_path.exists():
            print("错误: 找不到bundle文件")
            return False
        
        # 读取原始bundle
        with open(bundle_path, 'r', encoding='utf-8') as f:
            bundle_content = f.read()
        
        # 注入免费化补丁
        patch_code = self.generate_patch_code()
        
        # 在bundle开头注入补丁
        patched_content = patch_code + "\n" + bundle_content
        
        # 写回文件
        with open(bundle_path, 'w', encoding='utf-8') as f:
            f.write(patched_content)
        
        print("Bundle文件已修补")
        return True
    
    def generate_patch_code(self):
        """生成补丁代码"""
        return '''
// 训记应用免费化补丁 - 自动注入
(function() {
    console.log("训记免费化补丁开始加载...");
    
    // 等待应用初始化
    setTimeout(function() {
        try {
            // 修改网络请求函数
            if (typeof global !== 'undefined') {
                // 保存原始函数
                const originalPOST = global.POST;
                const originalGET = global.GET;
                
                // 模拟成功响应
                function createSuccessResponse(data = {}) {
                    return {
                        success: true,
                        res: data,
                        msg: "success"
                    };
                }
                
                // 模拟用户信息
                const mockUserInfo = {
                    nickname: "免费用户",
                    avatar: "https://tupian.xunjiapp.cn/default_avatar.png",
                    openid: "free_user_" + Date.now(),
                    vip: true,
                    vip_expire: Date.now() + 365 * 24 * 60 * 60 * 1000,
                    premium_features: true
                };
                
                // 重写POST函数
                global.POST = async function(url, data, timeout, errorCallback, successCallback) {
                    console.log("拦截POST请求:", url);
                    
                    // VIP验证接口
                    if (url.includes('/check_vip') || 
                        url.includes('/verify_payment') || 
                        url.includes('/subscription_status')) {
                        const response = createSuccessResponse({
                            vip: true,
                            premium: true,
                            subscription_active: true
                        });
                        successCallback && successCallback();
                        return response;
                    }
                    
                    // 用户信息接口
                    if (url.includes('/get_userinfo')) {
                        const response = createSuccessResponse(mockUserInfo);
                        successCallback && successCallback();
                        return response;
                    }
                    
                    // 登录接口
                    if (url.includes('/login') || url.includes('/auth')) {
                        const response = createSuccessResponse({
                            token: "free_access_token_" + Date.now(),
                            user: mockUserInfo
                        });
                        successCallback && successCallback();
                        return response;
                    }
                    
                    // 其他请求尝试原始逻辑，忽略jwt-error
                    try {
                        if (originalPOST) {
                            const result = await originalPOST(url, data, timeout, errorCallback, successCallback);
                            
                            if (result && result.msg === 'jwt-error') {
                                console.log("忽略JWT错误，返回成功响应");
                                return createSuccessResponse(data);
                            }
                            
                            return result;
                        }
                    } catch (error) {
                        console.log("网络请求失败，返回模拟成功响应:", error);
                    }
                    
                    successCallback && successCallback();
                    return createSuccessResponse(data);
                };
                
                // 重写GET函数
                global.GET = async function(url, debug) {
                    console.log("拦截GET请求:", url);
                    
                    if (url.includes('/get_userinfo')) {
                        return mockUserInfo;
                    }
                    
                    if (url.includes('/vip_status') || url.includes('/subscription')) {
                        return {
                            vip: true,
                            premium: true,
                            features: ['all_features_unlocked']
                        };
                    }
                    
                    try {
                        if (originalGET) {
                            const result = await originalGET(url, debug);
                            
                            if (result && result.msg === 'jwt-error') {
                                console.log("忽略JWT错误");
                                return {};
                            }
                            
                            return result;
                        }
                    } catch (error) {
                        console.log("GET请求失败:", error);
                    }
                    
                    return {};
                };
                
                // 禁用token清理
                global.removeScanCode = async function() {
                    console.log("禁用removeScanCode");
                    return Promise.resolve();
                };
                
                // 设置永久token
                global.token = "free_access_token_permanent";
                
                console.log("网络请求补丁已应用");
            }
            
            // 修改AsyncStorage
            if (typeof require !== 'undefined') {
                try {
                    const AsyncStorage = require('@react-native-async-storage/async-storage');
                    if (AsyncStorage && AsyncStorage.default) {
                        const storage = AsyncStorage.default;
                        const originalGetItem = storage.getItem;
                        
                        storage.getItem = async function(key) {
                            console.log("拦截存储读取:", key);
                            
                            // VIP相关键返回true
                            if (key === 'vip_status' || 
                                key === 'subscription_status' || 
                                key === 'premium_features') {
                                return Promise.resolve('true');
                            }
                            
                            // Token相关
                            if (key === 'token' || key === 'fake_token') {
                                return Promise.resolve('free_access_token_permanent');
                            }
                            
                            // 用户信息
                            if (key === 'nickname') {
                                return Promise.resolve('免费用户');
                            }
                            
                            try {
                                return await originalGetItem.call(this, key);
                            } catch (error) {
                                return null;
                            }
                        };
                        
                        console.log("存储补丁已应用");
                    }
                } catch (error) {
                    console.log("存储补丁应用失败:", error);
                }
            }
            
            console.log("训记免费化补丁加载完成!");
            
        } catch (error) {
            console.error("补丁加载失败:", error);
        }
    }, 2000);
})();
'''
    
    def modify_manifest(self):
        """修改AndroidManifest.xml（如果需要）"""
        print("检查AndroidManifest.xml...")
        
        manifest_path = self.work_dir / "AndroidManifest.xml"
        if manifest_path.exists():
            print("AndroidManifest.xml存在，但是二进制格式，跳过修改")
        
        return True
    
    def repack_apk(self):
        """重新打包APK"""
        print("正在重新打包APK...")

        # 生成新的APK文件名
        original_name = self.apk_path.stem
        self.patched_apk_path = self.apk_path.parent / f"{original_name}_free.apk"

        # 创建新的ZIP文件，特别处理native库
        with zipfile.ZipFile(self.patched_apk_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zip_ref:
            for root, dirs, files in os.walk(self.work_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(self.work_dir)

                    # 特殊处理native库文件 - 不压缩
                    if (str(arc_name).startswith('lib/') and str(arc_name).endswith('.so')) or \
                       str(arc_name).endswith('.so'):
                        print(f"添加native库（不压缩）: {arc_name}")
                        zip_ref.write(file_path, arc_name, compress_type=zipfile.ZIP_STORED)
                    else:
                        # 其他文件正常压缩
                        zip_ref.write(file_path, arc_name, compress_type=zipfile.ZIP_DEFLATED)

        print(f"新APK已生成: {self.patched_apk_path}")
        return True
    
    def cleanup(self):
        """清理临时文件"""
        if self.work_dir and self.work_dir.exists():
            shutil.rmtree(self.work_dir)
            print("临时文件已清理")
    
    def patch(self):
        """执行完整的补丁流程"""
        try:
            print("开始训记应用免费化补丁...")
            print(f"原始APK: {self.apk_path}")
            
            if not self.apk_path.exists():
                print("错误: APK文件不存在")
                return False
            
            # 提取APK
            if not self.extract_apk():
                return False
            
            # 修补bundle
            if not self.patch_bundle():
                return False
            
            # 修改manifest（可选）
            if not self.modify_manifest():
                return False
            
            # 重新打包
            if not self.repack_apk():
                return False
            
            print("\n" + "="*50)
            print("补丁应用成功!")
            print(f"免费版APK: {self.patched_apk_path}")
            print("="*50)
            
            return True
            
        except Exception as e:
            print(f"补丁失败: {e}")
            return False
        finally:
            self.cleanup()

def main():
    if len(sys.argv) != 2:
        print("用法: python repack_app.py <APK文件路径>")
        print("示例: python repack_app.py com.trainnote.rn_7.0.161.apk")
        sys.exit(1)
    
    apk_path = sys.argv[1]
    patcher = TrainNoteFreePatcher(apk_path)
    
    success = patcher.patch()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
