{"_from": "react-native-update@^5.9.1", "_id": "react-native-update@5.9.1", "_inBundle": false, "_integrity": "sha1-Hi7EM8zW5U0Wsju+8W617QF5aJA=", "_location": "/react-native-update", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "react-native-update@^5.9.1", "name": "react-native-update", "escapedName": "react-native-update", "rawSpec": "^5.9.1", "saveSpec": null, "fetchSpec": "^5.9.1"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npm.taobao.org/react-native-update/download/react-native-update-5.9.1.tgz", "_shasum": "1e2ec433ccd6e54d16b23bbef16eb5ed01796890", "_spec": "react-native-update@^5.9.1", "_where": "/Users/<USER>/Desktop/xunji-rn", "author": {"name": "reactnativecn"}, "bugs": {"url": "https://github.com/reactnativecn/react-native-pushy/issues"}, "bundleDependencies": false, "dependencies": {"uuid": "3"}, "deprecated": false, "description": "react-native hot update", "homepage": "https://github.com/reactnativecn/react-native-pushy#readme", "keywords": ["react-native", "ios", "android", "update"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "react-native-update", "peerDependencies": {"react-native": ">=0.27.0"}, "repository": {"type": "git", "url": "git+https://github.com/reactnativecn/react-native-pushy.git"}, "scripts": {"build-lib": "$ANDROID_HOME/ndk-bundle/ndk-build NDK_PROJECT_PATH=android APP_BUILD_SCRIPT=android/jni/Android.mk NDK_LIBS_OUT=android/lib", "test": "echo \"Error: no test specified\" && exit 1"}, "version": "5.9.1"}