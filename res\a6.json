{"_args": [["react-native-code-push@5.6.0", "/Users/<USER>/Desktop/xunji-rn"]], "_from": "react-native-code-push@5.6.0", "_id": "react-native-code-push@5.6.0", "_inBundle": false, "_integrity": "sha512-NDZG+t1K+7ubjdtkuQ5UjcqRZbtqZMiMPCnp3VJRfdcX2KcgivW0piRZ3myuAYd6Jyxs4w13rIXo2kzLW4sxBQ==", "_location": "/react-native-code-push", "_phantomChildren": {"agent-base": "4.3.0", "big-integer": "1.6.48", "cli-width": "2.2.0", "code-point-at": "1.1.0", "component-emitter": "1.3.0", "cookiejar": "2.1.2", "escape-string-regexp": "1.0.5", "exit-hook": "1.1.1", "extend": "3.0.2", "form-data": "2.3.3", "formidable": "1.2.2", "get-uri": "2.0.4", "has-ansi": "2.0.0", "http-proxy-agent": "2.1.0", "inflight": "1.0.6", "inherits": "2.0.4", "ip": "1.1.5", "lodash": "4.17.20", "lru-cache": "4.1.5", "methods": "1.1.2", "mime": "1.6.0", "minimatch": "3.0.4", "ms": "2.1.2", "number-is-nan": "1.0.1", "object-assign": "4.1.1", "once": "1.4.0", "os-tmpdir": "1.0.2", "pac-resolver": "3.0.0", "path-is-absolute": "1.0.1", "pegjs": "0.10.0", "pinkie-promise": "2.0.1", "proxy-from-env": "1.1.0", "q": "1.5.1", "qs": "6.9.4", "raw-body": "2.4.1", "readable-stream": "2.3.6", "run-async": "2.4.1", "rx": "4.1.0", "spawn-sync": "1.0.15", "stream-buffers": "2.2.0", "through": "2.3.8", "xmldom": "0.1.31", "yazl": "2.5.1"}, "_requested": {"type": "version", "registry": true, "raw": "react-native-code-push@5.6.0", "name": "react-native-code-push", "escapedName": "react-native-code-push", "rawSpec": "5.6.0", "saveSpec": null, "fetchSpec": "5.6.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react-native-code-push/-/react-native-code-push-5.6.0.tgz", "_spec": "5.6.0", "_where": "/Users/<USER>/Desktop/xunji-rn", "author": {"name": "Microsoft Corporation"}, "bugs": {"url": "https://github.com/Microsoft/react-native-code-push/issues"}, "dependencies": {"code-push": "2.0.6", "glob": "^5.0.15", "hoist-non-react-statics": "^2.3.1", "inquirer": "1.1.2", "plist": "3.0.1", "semver": "^5.6.0", "xcode": "1.0.0"}, "description": "React Native plugin for the CodePush service", "devDependencies": {"archiver": "latest", "body-parser": "latest", "code-push-plugin-testing-framework": "file:./code-push-plugin-testing-framework", "del": "latest", "express": "latest", "gulp-insert": "latest", "gulp-tslint": "latest", "gulp-typescript": "2.12.2", "mkdirp": "latest", "q": "^1.4.1", "run-sequence": "latest", "tslint": "^4.3.1", "typescript": "^2.1.5"}, "homepage": "https://microsoft.github.io/code-push", "keywords": ["react-native", "code", "push"], "license": "MIT", "main": "CodePush.js", "name": "react-native-code-push", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/react-native-code-push.git"}, "rnpm": {"android": {"packageInstance": "new CodePush(getResources().getString(R.string.reactNativeCodePush_androidDeploymentKey), getApplicationContext(), BuildConfig.DEBUG)"}, "ios": {"sharedLibraries": ["libz"]}, "commands": {"postlink": "node node_modules/react-native-code-push/scripts/postlink/run"}}, "typings": "typings/react-native-code-push.d.ts", "version": "5.6.0"}