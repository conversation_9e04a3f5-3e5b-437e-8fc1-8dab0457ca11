#!/usr/bin/env node
/**
 * 训记应用智能代理服务器
 * 用于拦截和修改API请求，实现免费化功能
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = 8888;

// 原始API服务器地址
const TARGET_API = 'https://api.xunjiapp.cn';
const TARGET_FOOD_API = 'http://eatings.xunjiapp.cn';
const TARGET_CDN = 'https://tupian.xunjiapp.cn';

// 启用CORS和JSON解析
app.use(cors());
app.use(express.json());

// 模拟用户数据
const mockUserData = {
    nickname: "免费用户",
    avatar: "https://tupian.xunjiapp.cn/default_avatar.png",
    openid: "free_user_" + Date.now(),
    vip: true,
    vip_expire: Date.now() + 365 * 24 * 60 * 60 * 1000,
    premium_features: true,
    subscription_status: "active",
    user_level: "premium",
    features_unlocked: true
};

// 生成高级功能模拟数据
function generatePremiumData(endpoint) {
    const premiumData = {
        '/premium_plans': {
            plans: [
                {
                    id: 1,
                    name: "专业力量训练计划",
                    description: "由专业教练设计的系统性力量训练计划",
                    duration: "12周",
                    level: "中高级",
                    exercises: [
                        { name: "深蹲", sets: 5, reps: "5-8", intensity: "85%" },
                        { name: "卧推", sets: 4, reps: "6-10", intensity: "80%" },
                        { name: "硬拉", sets: 3, reps: "3-5", intensity: "90%" }
                    ],
                    progression: "每周递增2.5kg"
                }
            ],
            total: 25,
            categories: ["力量", "减脂", "增肌", "功能性"]
        },
        
        '/advanced_stats': {
            overview: {
                total_workouts: 156,
                total_volume: 45600,
                pr_count: 23,
                consistency: 85
            },
            trends: {
                strength: "+15%",
                endurance: "+8%",
                body_composition: "improving"
            },
            detailed_metrics: {
                squat_progress: [100, 105, 110, 115, 120],
                bench_progress: [70, 75, 80, 85, 90],
                deadlift_progress: [120, 125, 130, 135, 140]
            },
            recommendations: [
                "增加下肢训练频率",
                "注意训练后恢复",
                "优化营养摄入"
            ]
        },
        
        '/nutrition_analysis': {
            daily_target: {
                calories: 2200,
                protein: 150,
                carbs: 250,
                fat: 80
            },
            meal_suggestions: [
                {
                    meal: "早餐",
                    foods: ["燕麦", "鸡蛋", "牛奶", "香蕉"],
                    calories: 450
                },
                {
                    meal: "训练后",
                    foods: ["乳清蛋白", "香蕉", "蜂蜜"],
                    calories: 300
                }
            ],
            supplements: [
                { name: "乳清蛋白", timing: "训练后30分钟" },
                { name: "肌酸", timing: "训练前" },
                { name: "维生素D", timing: "随餐" }
            ]
        }
    };
    
    return premiumData[endpoint] || { 
        message: "高级功能数据", 
        access_granted: true 
    };
}

// VIP验证接口拦截
app.all('/check_vip', (req, res) => {
    console.log('拦截VIP验证请求');
    res.json({
        success: true,
        res: {
            vip: true,
            premium: true,
            subscription_active: true,
            expires_at: Date.now() + 365 * 24 * 60 * 60 * 1000
        }
    });
});

// 用户信息接口拦截
app.all('/get_userinfo', (req, res) => {
    console.log('拦截用户信息请求');
    res.json({
        success: true,
        res: mockUserData
    });
});

// 高级功能接口拦截
app.all('/premium_*', (req, res) => {
    console.log('拦截高级功能请求:', req.path);
    const data = generatePremiumData(req.path);
    res.json({
        success: true,
        res: data
    });
});

// 营养数据接口拦截
app.all('/nutrition_*', (req, res) => {
    console.log('拦截营养数据请求:', req.path);
    const data = generatePremiumData(req.path);
    res.json({
        success: true,
        res: data
    });
});

// 统计分析接口拦截
app.all('/advanced_stats*', (req, res) => {
    console.log('拦截统计分析请求:', req.path);
    const data = generatePremiumData('/advanced_stats');
    res.json({
        success: true,
        res: data
    });
});

// 通用代理中间件 - 处理其他请求
const apiProxy = createProxyMiddleware({
    target: TARGET_API,
    changeOrigin: true,
    onProxyReq: (proxyReq, req, res) => {
        console.log('代理请求:', req.method, req.path);
        
        // 确保所有请求都带有VIP token
        proxyReq.setHeader('Authorization', 'Bearer free_access_token_permanent');
    },
    onProxyRes: (proxyRes, req, res) => {
        console.log('代理响应:', proxyRes.statusCode, req.path);
        
        // 拦截响应数据
        let body = '';
        proxyRes.on('data', (chunk) => {
            body += chunk;
        });
        
        proxyRes.on('end', () => {
            try {
                const data = JSON.parse(body);
                
                // 处理JWT错误
                if (data.msg === 'jwt-error') {
                    console.log('检测到JWT错误，返回成功响应');
                    res.json({
                        success: true,
                        res: {},
                        msg: 'success'
                    });
                    return;
                }
                
                // 解除VIP限制
                if (data.res && data.res.vip_required) {
                    console.log('解除VIP限制');
                    data.res.vip_required = false;
                    data.res.access_granted = true;
                }
                
                // 修改用户信息
                if (req.path.includes('userinfo') && data.res) {
                    console.log('修改用户信息为VIP');
                    Object.assign(data.res, mockUserData);
                }
                
            } catch (error) {
                console.log('响应解析失败:', error);
            }
        });
    },
    onError: (err, req, res) => {
        console.log('代理错误:', err.message);
        
        // 网络错误时返回模拟数据
        if (req.path.includes('premium') || req.path.includes('advanced')) {
            const data = generatePremiumData(req.path);
            res.json({
                success: true,
                res: data
            });
        } else {
            res.json({
                success: true,
                res: {},
                msg: 'offline_mode'
            });
        }
    }
});

// 食物API代理
const foodProxy = createProxyMiddleware({
    target: TARGET_FOOD_API,
    changeOrigin: true,
    pathRewrite: {
        '^/food': ''
    }
});

// CDN代理
const cdnProxy = createProxyMiddleware({
    target: TARGET_CDN,
    changeOrigin: true,
    pathRewrite: {
        '^/cdn': ''
    }
});

// 路由设置
app.use('/food', foodProxy);
app.use('/cdn', cdnProxy);
app.use('/', apiProxy);

// 启动服务器
app.listen(PORT, () => {
    console.log(`训记代理服务器已启动: http://localhost:${PORT}`);
    console.log('请将应用的API地址修改为: http://localhost:' + PORT);
    console.log('或者使用hosts文件重定向: api.xunjiapp.cn -> 127.0.0.1');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭代理服务器...');
    process.exit(0);
});
