#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK签名工具
用于为修改后的APK文件添加数字签名
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

class APKSigner:
    def __init__(self, apk_path):
        self.apk_path = Path(apk_path)
        self.signed_apk_path = None
        self.keystore_path = None
        
    def create_keystore(self):
        """创建用于签名的keystore文件"""
        print("正在创建签名密钥...")
        
        # 在当前目录创建keystore
        self.keystore_path = Path("trainnote_debug.keystore")
        
        # 如果keystore已存在，直接使用
        if self.keystore_path.exists():
            print(f"使用现有keystore: {self.keystore_path}")
            return True
        
        # 创建新的keystore
        keytool_cmd = [
            "keytool",
            "-genkey",
            "-v",
            "-keystore", str(self.keystore_path),
            "-alias", "trainnote_debug",
            "-keyalg", "RSA",
            "-keysize", "2048",
            "-validity", "10000",
            "-storepass", "123456",
            "-keypass", "123456",
            "-dname", "CN=TrainNote Debug, OU=Debug, O=Debug, L=Debug, S=Debug, C=CN"
        ]
        
        try:
            result = subprocess.run(keytool_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"Keystore创建成功: {self.keystore_path}")
                return True
            else:
                print(f"Keystore创建失败: {result.stderr}")
                return False
        except FileNotFoundError:
            print("错误: 找不到keytool命令，请确保Java JDK已安装并在PATH中")
            return False
    
    def sign_apk_with_apksigner(self):
        """使用apksigner工具签名APK"""
        print("正在使用apksigner签名APK...")
        
        # 生成签名后的APK文件名
        original_name = self.apk_path.stem
        self.signed_apk_path = self.apk_path.parent / f"{original_name}_signed.apk"
        
        # apksigner命令
        apksigner_cmd = [
            "apksigner",
            "sign",
            "--ks", str(self.keystore_path),
            "--ks-key-alias", "trainnote_debug",
            "--ks-pass", "pass:123456",
            "--key-pass", "pass:123456",
            "--out", str(self.signed_apk_path),
            str(self.apk_path)
        ]
        
        try:
            result = subprocess.run(apksigner_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"APK签名成功: {self.signed_apk_path}")
                return True
            else:
                print(f"APK签名失败: {result.stderr}")
                return False
        except FileNotFoundError:
            print("错误: 找不到apksigner命令")
            return False
    
    def sign_apk_with_jarsigner(self):
        """使用jarsigner工具签名APK（备用方法）"""
        print("正在使用jarsigner签名APK...")
        
        # 生成签名后的APK文件名
        original_name = self.apk_path.stem
        self.signed_apk_path = self.apk_path.parent / f"{original_name}_signed.apk"
        
        # 先复制APK文件
        import shutil
        shutil.copy2(self.apk_path, self.signed_apk_path)
        
        # jarsigner命令
        jarsigner_cmd = [
            "jarsigner",
            "-verbose",
            "-sigalg", "SHA1withRSA",
            "-digestalg", "SHA1",
            "-keystore", str(self.keystore_path),
            "-storepass", "123456",
            "-keypass", "123456",
            str(self.signed_apk_path),
            "trainnote_debug"
        ]
        
        try:
            result = subprocess.run(jarsigner_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"APK签名成功: {self.signed_apk_path}")
                return True
            else:
                print(f"APK签名失败: {result.stderr}")
                return False
        except FileNotFoundError:
            print("错误: 找不到jarsigner命令")
            return False
    
    def verify_signature(self):
        """验证APK签名"""
        print("正在验证APK签名...")
        
        # 使用apksigner验证
        verify_cmd = [
            "apksigner",
            "verify",
            "-v",
            str(self.signed_apk_path)
        ]
        
        try:
            result = subprocess.run(verify_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("APK签名验证成功")
                return True
            else:
                print(f"APK签名验证失败: {result.stderr}")
                return False
        except FileNotFoundError:
            # 如果没有apksigner，使用jarsigner验证
            verify_cmd = [
                "jarsigner",
                "-verify",
                "-verbose",
                "-certs",
                str(self.signed_apk_path)
            ]
            
            try:
                result = subprocess.run(verify_cmd, capture_output=True, text=True)
                if "jar verified" in result.stdout:
                    print("APK签名验证成功")
                    return True
                else:
                    print(f"APK签名验证失败: {result.stdout}")
                    return False
            except FileNotFoundError:
                print("警告: 无法验证签名，但APK可能已正确签名")
                return True
    
    def sign(self):
        """执行完整的签名流程"""
        try:
            print("开始APK签名流程...")
            print(f"输入APK: {self.apk_path}")
            
            if not self.apk_path.exists():
                print("错误: APK文件不存在")
                return False
            
            # 创建keystore
            if not self.create_keystore():
                return False
            
            # 尝试使用apksigner签名
            if not self.sign_apk_with_apksigner():
                print("apksigner签名失败，尝试使用jarsigner...")
                if not self.sign_apk_with_jarsigner():
                    print("所有签名方法都失败了")
                    return False
            
            # 验证签名
            if not self.verify_signature():
                print("警告: 签名验证失败，但APK可能仍然可用")
            
            print("\n" + "="*50)
            print("APK签名完成!")
            print(f"签名后的APK: {self.signed_apk_path}")
            print("现在可以尝试安装了:")
            print(f"adb install {self.signed_apk_path}")
            print("="*50)
            
            return True
            
        except Exception as e:
            print(f"签名失败: {e}")
            return False

def main():
    if len(sys.argv) != 2:
        print("用法: python sign_apk.py <APK文件路径>")
        print("示例: python sign_apk.py com.trainnote.rn_7.0.161_free.apk")
        sys.exit(1)
    
    apk_path = sys.argv[1]
    signer = APKSigner(apk_path)
    
    success = signer.sign()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
