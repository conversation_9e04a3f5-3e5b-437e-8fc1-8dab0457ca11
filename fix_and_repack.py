#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训记应用修复和重新打包工具
专门处理native库和APK结构问题
"""

import os
import sys
import shutil
import subprocess
import tempfile
import zipfile
import re
from pathlib import Path

class APKFixer:
    def __init__(self, apk_path):
        self.apk_path = Path(apk_path)
        self.work_dir = None
        self.fixed_apk_path = None
        
    def extract_apk_properly(self):
        """正确提取APK，保持文件结构"""
        print("正在提取APK文件...")
        self.work_dir = Path(tempfile.mkdtemp(prefix="trainnote_fix_"))
        
        # 使用zipfile提取，保持原始压缩状态信息
        with zipfile.ZipFile(self.apk_path, 'r') as zip_ref:
            # 记录每个文件的压缩信息
            self.file_info = {}
            for info in zip_ref.infolist():
                self.file_info[info.filename] = {
                    'compress_type': info.compress_type,
                    'compress_size': info.compress_size,
                    'file_size': info.file_size
                }
            
            # 提取所有文件
            zip_ref.extractall(self.work_dir)
        
        print(f"APK已提取到: {self.work_dir}")
        return True
    
    def analyze_native_libs(self):
        """分析native库结构"""
        print("正在分析native库...")
        
        lib_dir = self.work_dir / "lib"
        if not lib_dir.exists():
            print("未找到lib目录")
            return True
        
        # 检查各个架构的库文件
        for arch_dir in lib_dir.iterdir():
            if arch_dir.is_dir():
                print(f"发现架构: {arch_dir.name}")
                for so_file in arch_dir.glob("*.so"):
                    print(f"  - {so_file.name} ({so_file.stat().st_size} bytes)")
        
        return True
    
    def patch_bundle_carefully(self):
        """小心地修补bundle文件"""
        print("正在修补bundle文件...")
        
        bundle_path = self.work_dir / "assets" / "index.android.bundle"
        if not bundle_path.exists():
            print("错误: 找不到bundle文件")
            return False
        
        # 备份原始bundle
        backup_path = bundle_path.with_suffix('.bundle.backup')
        shutil.copy2(bundle_path, backup_path)
        
        try:
            # 读取原始bundle
            with open(bundle_path, 'r', encoding='utf-8') as f:
                bundle_content = f.read()
            
            # 生成更小的补丁代码
            patch_code = self.generate_minimal_patch()
            
            # 在bundle开头注入补丁
            patched_content = patch_code + "\n" + bundle_content
            
            # 写回文件
            with open(bundle_path, 'w', encoding='utf-8') as f:
                f.write(patched_content)
            
            print("Bundle文件已修补")
            return True
            
        except Exception as e:
            print(f"Bundle修补失败: {e}")
            # 恢复备份
            shutil.copy2(backup_path, bundle_path)
            return False
    
    def generate_minimal_patch(self):
        """生成最小化的补丁代码"""
        return '''
// 训记免费化补丁 v2.0
(function(){
    var patchLoaded = false;
    function loadPatch() {
        if (patchLoaded) return;
        patchLoaded = true;
        
        try {
            // 拦截网络请求
            if (typeof global !== 'undefined' && global.POST) {
                var originalPOST = global.POST;
                global.POST = function(url, data, timeout, errorCallback, successCallback) {
                    if (url.includes('/check_vip') || url.includes('/verify_payment')) {
                        var response = { success: true, res: { vip: true, premium: true } };
                        successCallback && successCallback();
                        return Promise.resolve(response);
                    }
                    if (url.includes('/get_userinfo')) {
                        var userInfo = { nickname: "免费用户", vip: true, openid: "free_" + Date.now() };
                        var response = { success: true, res: userInfo };
                        successCallback && successCallback();
                        return Promise.resolve(response);
                    }
                    return originalPOST.apply(this, arguments).catch(function(error) {
                        if (error && error.msg === 'jwt-error') {
                            successCallback && successCallback();
                            return { success: true, res: data };
                        }
                        throw error;
                    });
                };
                
                // 设置永久token
                global.token = "free_token_permanent";
                
                // 禁用token清理
                global.removeScanCode = function() { return Promise.resolve(); };
            }
            
            console.log("训记免费化补丁已加载");
        } catch (e) {
            console.error("补丁加载失败:", e);
        }
    }
    
    // 延迟加载补丁
    setTimeout(loadPatch, 1000);
})();
'''
    
    def repack_apk_properly(self):
        """正确重新打包APK"""
        print("正在重新打包APK...")
        
        # 生成新的APK文件名
        original_name = self.apk_path.stem
        self.fixed_apk_path = self.apk_path.parent / f"{original_name}_fixed.apk"
        
        # 创建新的ZIP文件，严格按照原始压缩方式
        with zipfile.ZipFile(self.fixed_apk_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zip_ref:
            for root, dirs, files in os.walk(self.work_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = str(file_path.relative_to(self.work_dir)).replace('\\', '/')
                    
                    # 获取原始压缩信息
                    original_info = self.file_info.get(arc_name, {})
                    original_compress_type = original_info.get('compress_type', zipfile.ZIP_DEFLATED)
                    
                    # 特殊处理规则
                    if arc_name.startswith('lib/') and arc_name.endswith('.so'):
                        # Native库不压缩
                        compress_type = zipfile.ZIP_STORED
                        print(f"添加native库（不压缩）: {arc_name}")
                    elif arc_name in ['AndroidManifest.xml', 'resources.arsc']:
                        # 系统文件保持原始压缩方式
                        compress_type = original_compress_type
                        print(f"添加系统文件: {arc_name}")
                    elif arc_name.startswith('META-INF/'):
                        # 签名相关文件
                        compress_type = zipfile.ZIP_DEFLATED
                        print(f"添加META-INF文件: {arc_name}")
                    else:
                        # 其他文件使用原始压缩方式或默认压缩
                        compress_type = original_compress_type
                    
                    try:
                        zip_ref.write(file_path, arc_name, compress_type=compress_type)
                    except Exception as e:
                        print(f"警告: 添加文件失败 {arc_name}: {e}")
                        # 尝试使用默认压缩方式
                        zip_ref.write(file_path, arc_name, compress_type=zipfile.ZIP_DEFLATED)
        
        print(f"修复后的APK已生成: {self.fixed_apk_path}")
        return True
    
    def cleanup(self):
        """清理临时文件"""
        if self.work_dir and self.work_dir.exists():
            shutil.rmtree(self.work_dir)
            print("临时文件已清理")
    
    def fix_and_repack(self):
        """执行完整的修复和重新打包流程"""
        try:
            print("开始APK修复和重新打包...")
            print(f"原始APK: {self.apk_path}")
            
            if not self.apk_path.exists():
                print("错误: APK文件不存在")
                return False
            
            # 提取APK
            if not self.extract_apk_properly():
                return False
            
            # 分析native库
            if not self.analyze_native_libs():
                return False
            
            # 修补bundle
            if not self.patch_bundle_carefully():
                return False
            
            # 重新打包
            if not self.repack_apk_properly():
                return False
            
            print("\n" + "="*50)
            print("APK修复和重新打包成功!")
            print(f"修复后的APK: {self.fixed_apk_path}")
            print("\n下一步:")
            print(f"1. 签名APK: python sign_apk.py {self.fixed_apk_path}")
            print("2. 安装APK: adb install <签名后的APK>")
            print("="*50)
            
            return True
            
        except Exception as e:
            print(f"修复失败: {e}")
            return False
        finally:
            self.cleanup()

def main():
    if len(sys.argv) != 2:
        print("用法: python fix_and_repack.py <APK文件路径>")
        print("示例: python fix_and_repack.py com.trainnote.rn_7.0.161.apk")
        sys.exit(1)
    
    apk_path = sys.argv[1]
    fixer = APKFixer(apk_path)
    
    success = fixer.fix_and_repack()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
