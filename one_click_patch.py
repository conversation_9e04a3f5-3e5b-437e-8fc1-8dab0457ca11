#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训记应用一键免费化工具
自动完成修复、重新打包、签名的完整流程
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def run_command(cmd, description=""):
    """运行命令并处理结果"""
    print(f"\n正在执行: {description}")
    print(f"命令: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    
    try:
        if isinstance(cmd, str):
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        else:
            result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ {description} 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"✗ {description} 失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"✗ {description} 异常: {e}")
        return False

def check_prerequisites():
    """检查必要的工具"""
    print("检查必要工具...")
    
    tools = {
        'python': 'python --version',
        'java': 'java -version',
        'keytool': 'keytool -help',
        'adb': 'adb version'
    }
    
    missing_tools = []
    for tool, cmd in tools.items():
        if not run_command(cmd, f"检查{tool}"):
            missing_tools.append(tool)
    
    if missing_tools:
        print(f"\n缺少必要工具: {', '.join(missing_tools)}")
        print("\n请安装以下工具:")
        if 'java' in missing_tools:
            print("- Java JDK 8+")
        if 'adb' in missing_tools:
            print("- Android SDK Platform Tools")
        return False
    
    print("✓ 所有必要工具已就绪")
    return True

def create_debug_keystore():
    """创建调试密钥库"""
    keystore_path = Path("debug.keystore")
    
    if keystore_path.exists():
        print("✓ 调试密钥库已存在")
        return True
    
    cmd = [
        "keytool", "-genkey", "-v",
        "-keystore", str(keystore_path),
        "-alias", "androiddebugkey",
        "-keyalg", "RSA",
        "-keysize", "2048",
        "-validity", "10000",
        "-storepass", "android",
        "-keypass", "android",
        "-dname", "CN=Android Debug,O=Android,C=US"
    ]
    
    return run_command(cmd, "创建调试密钥库")

def patch_apk(apk_path):
    """修补APK"""
    print(f"\n开始修补APK: {apk_path}")
    
    # 使用修复工具
    cmd = [sys.executable, "fix_and_repack.py", str(apk_path)]
    if not run_command(cmd, "修补APK"):
        return None
    
    # 查找生成的文件
    apk_path = Path(apk_path)
    fixed_apk = apk_path.parent / f"{apk_path.stem}_fixed.apk"
    
    if fixed_apk.exists():
        return fixed_apk
    else:
        print("✗ 找不到修补后的APK文件")
        return None

def sign_apk(apk_path):
    """签名APK"""
    print(f"\n开始签名APK: {apk_path}")
    
    apk_path = Path(apk_path)
    signed_apk = apk_path.parent / f"{apk_path.stem}_signed.apk"
    
    # 尝试使用apksigner
    cmd = [
        "apksigner", "sign",
        "--ks", "debug.keystore",
        "--ks-key-alias", "androiddebugkey",
        "--ks-pass", "pass:android",
        "--key-pass", "pass:android",
        "--out", str(signed_apk),
        str(apk_path)
    ]
    
    if run_command(cmd, "使用apksigner签名"):
        return signed_apk
    
    # 如果apksigner失败，尝试jarsigner
    print("apksigner失败，尝试jarsigner...")
    
    # 先复制文件
    import shutil
    shutil.copy2(apk_path, signed_apk)
    
    cmd = [
        "jarsigner",
        "-verbose",
        "-sigalg", "SHA1withRSA",
        "-digestalg", "SHA1",
        "-keystore", "debug.keystore",
        "-storepass", "android",
        "-keypass", "android",
        str(signed_apk),
        "androiddebugkey"
    ]
    
    if run_command(cmd, "使用jarsigner签名"):
        return signed_apk
    
    print("✗ 所有签名方法都失败了")
    return None

def install_apk(apk_path):
    """安装APK"""
    print(f"\n开始安装APK: {apk_path}")
    
    # 先卸载原版应用
    run_command(["adb", "uninstall", "com.trainnote.rn"], "卸载原版应用")
    
    # 安装新APK
    cmd = ["adb", "install", str(apk_path)]
    return run_command(cmd, "安装APK")

def main():
    print("="*60)
    print("训记应用一键免费化工具")
    print("="*60)
    
    if len(sys.argv) != 2:
        print("用法: python one_click_patch.py <APK文件路径>")
        print("示例: python one_click_patch.py com.trainnote.rn_7.0.161.apk")
        sys.exit(1)
    
    apk_path = Path(sys.argv[1])
    if not apk_path.exists():
        print(f"错误: APK文件不存在: {apk_path}")
        sys.exit(1)
    
    print(f"目标APK: {apk_path}")
    
    # 检查工具
    if not check_prerequisites():
        print("\n请安装必要工具后重试")
        sys.exit(1)
    
    # 创建密钥库
    if not create_debug_keystore():
        print("\n密钥库创建失败")
        sys.exit(1)
    
    # 修补APK
    fixed_apk = patch_apk(apk_path)
    if not fixed_apk:
        print("\nAPK修补失败")
        sys.exit(1)
    
    # 签名APK
    signed_apk = sign_apk(fixed_apk)
    if not signed_apk:
        print("\nAPK签名失败")
        sys.exit(1)
    
    # 验证签名
    cmd = ["apksigner", "verify", "-v", str(signed_apk)]
    if not run_command(cmd, "验证APK签名"):
        print("警告: 签名验证失败，但APK可能仍然可用")
    
    # 询问是否安装
    print(f"\n✓ 免费版APK已准备就绪: {signed_apk}")
    
    while True:
        choice = input("\n是否立即安装到设备? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            if install_apk(signed_apk):
                print("\n🎉 安装成功! 训记应用已免费化!")
                print("\n使用说明:")
                print("1. 打开应用，所有功能都应该可以免费使用")
                print("2. 如果遇到登录问题，可以尝试跳过登录")
                print("3. VIP功能应该已经解锁")
            else:
                print("\n安装失败，请手动安装:")
                print(f"adb install {signed_apk}")
            break
        elif choice in ['n', 'no']:
            print(f"\n手动安装命令:")
            print(f"adb install {signed_apk}")
            break
        else:
            print("请输入 y 或 n")
    
    print("\n" + "="*60)
    print("处理完成!")
    print("="*60)

if __name__ == "__main__":
    main()
