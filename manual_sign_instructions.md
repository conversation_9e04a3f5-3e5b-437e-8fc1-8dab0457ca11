# 手动APK签名指南

## 方法一：使用Android SDK工具

### 1. 安装必要工具

确保已安装：
- Java JDK 8+
- Android SDK Build Tools

### 2. 创建调试密钥库

```bash
# 创建调试用的keystore
keytool -genkey -v -keystore debug.keystore -alias androiddebugkey -keyalg RSA -keysize 2048 -validity 10000 -storepass android -keypass android -dname "CN=Android Debug,O=Android,C=US"
```

### 3. 使用apksigner签名

```bash
# 使用apksigner签名APK
apksigner sign --ks debug.keystore --ks-key-alias androiddebugkey --ks-pass pass:android --key-pass pass:android --out com.trainnote.rn_7.0.161_free_signed.apk com.trainnote.rn_7.0.161_free.apk
```

### 4. 验证签名

```bash
# 验证APK签名
apksigner verify -v com.trainnote.rn_7.0.161_free_signed.apk
```

## 方法二：使用jarsigner（备用）

### 1. 创建密钥库

```bash
keytool -genkey -v -keystore my-release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000
```

### 2. 签名APK

```bash
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore com.trainnote.rn_7.0.161_free.apk alias_name
```

### 3. 对齐APK（可选）

```bash
zipalign -v 4 com.trainnote.rn_7.0.161_free.apk com.trainnote.rn_7.0.161_free_aligned.apk
```

## 方法三：使用在线工具

### APK签名在线工具
1. 访问 https://www.apksigner.com/
2. 上传APK文件
3. 下载签名后的APK

### 注意事项
- 在线工具可能有安全风险
- 建议仅用于测试目的

## 方法四：使用uber-apk-signer

### 1. 下载工具

从GitHub下载：https://github.com/patrickfav/uber-apk-signer/releases

### 2. 使用命令

```bash
# 简单签名
java -jar uber-apk-signer.jar --apks com.trainnote.rn_7.0.161_free.apk

# 或者直接使用可执行文件
uber-apk-signer --apks com.trainnote.rn_7.0.161_free.apk
```

## 常见问题解决

### 1. 找不到keytool命令

**Windows:**
```cmd
set PATH=%PATH%;C:\Program Files\Java\jdk1.8.0_XXX\bin
```

**Linux/Mac:**
```bash
export PATH=$PATH:$JAVA_HOME/bin
```

### 2. 找不到apksigner命令

**添加Android SDK到PATH:**
```bash
# Windows
set PATH=%PATH%;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\XX.X.X

# Linux/Mac
export PATH=$PATH:~/Android/Sdk/build-tools/XX.X.X
```

### 3. 签名验证失败

- 确保使用正确的密钥库密码
- 检查APK文件是否损坏
- 尝试重新创建密钥库

### 4. 安装时仍然报错

可能的原因：
- 设备上已安装同包名的应用（需要先卸载）
- APK文件损坏
- 设备不允许安装未知来源应用

解决方法：
```bash
# 卸载现有应用
adb uninstall com.trainnote.rn

# 启用未知来源安装
adb shell settings put global install_non_market_apps 1

# 重新安装
adb install com.trainnote.rn_7.0.161_free_signed.apk
```

## 推荐流程

1. **首选**: 使用Python脚本 `sign_apk.py`
2. **备选**: 使用uber-apk-signer工具
3. **手动**: 使用Android SDK工具
4. **最后**: 使用在线签名工具

## 安全提醒

- 调试密钥仅用于测试
- 不要在生产环境使用调试签名
- 保护好您的签名密钥
- 定期备份密钥库文件
