// 训记应用免费化补丁 - 网络请求修改（增强版）
// 这个文件包含了修改网络请求逻辑的代码，支持后端依赖功能

// 1. 修改JWT错误处理逻辑
// 原始代码中，当收到 jwt-error 时会清除token并重启应用
// 我们需要绕过这个检查

const originalPOST = global.POST;
const originalGET = global.GET;
const originalPOST_GZIP = global.POST_GZIP;
const originalFood_POST = global.Food_POST;
const originalFood_GET = global.Food_GET;

// 模拟成功响应的函数
function createSuccessResponse(data = {}) {
    return {
        success: true,
        res: data,
        msg: "success"
    };
}

// 模拟用户信息
const mockUserInfo = {
    nickname: "免费用户",
    avatar: "https://tupian.xunjiapp.cn/default_avatar.png",
    openid: "free_user_" + Date.now(),
    vip: true, // 设置为VIP用户
    vip_expire: Date.now() + 365 * 24 * 60 * 60 * 1000, // 一年后过期
    premium_features: true,
    subscription_status: "active",
    user_level: "premium"
};

// 需要完全拦截的VIP验证接口
const VIP_CHECK_ENDPOINTS = [
    '/check_vip',
    '/verify_payment',
    '/subscription_status',
    '/user_level',
    '/premium_check',
    '/vip_status',
    '/payment_verify'
];

// 需要修改响应数据的接口
const DATA_MODIFY_ENDPOINTS = [
    '/get_userinfo',
    '/user_profile',
    '/account_info'
];

// 可能需要伪造数据的高级功能接口
const PREMIUM_FEATURE_ENDPOINTS = [
    '/premium_plans',
    '/advanced_stats',
    '/pro_workouts',
    '/nutrition_data',
    '/expert_guidance'
];

// 生成模拟的高级功能数据
function generateMockPremiumData(url) {
    console.log("生成模拟数据for:", url);

    if (url.includes('/premium_plans') || url.includes('/pro_workouts')) {
        return {
            plans: [
                {
                    id: 1,
                    name: "高级力量训练计划",
                    description: "专业教练设计的力量训练计划",
                    duration: "8周",
                    level: "中高级",
                    exercises: [
                        { name: "深蹲", sets: 4, reps: "8-12", rest: "2-3分钟" },
                        { name: "卧推", sets: 4, reps: "6-10", rest: "2-3分钟" },
                        { name: "硬拉", sets: 3, reps: "5-8", rest: "3-4分钟" },
                        { name: "引体向上", sets: 3, reps: "最大次数", rest: "2分钟" }
                    ]
                },
                {
                    id: 2,
                    name: "高级减脂训练计划",
                    description: "高效燃脂的HIIT训练计划",
                    duration: "6周",
                    level: "中级",
                    exercises: [
                        { name: "波比跳", sets: 4, reps: "30秒", rest: "30秒" },
                        { name: "高抬腿", sets: 4, reps: "30秒", rest: "30秒" },
                        { name: "俯卧撑", sets: 4, reps: "最大次数", rest: "1分钟" }
                    ]
                }
            ],
            total_plans: 15,
            categories: ["力量训练", "减脂塑形", "增肌增重", "功能性训练"]
        };
    }

    if (url.includes('/advanced_stats') || url.includes('/analytics')) {
        return {
            total_workouts: 156,
            total_volume: 45600,
            pr_records: 23,
            consistency_score: 85,
            strength_progress: "+15%",
            weekly_trend: "上升",
            monthly_stats: {
                workouts_completed: 18,
                total_sets: 324,
                total_reps: 2880,
                average_intensity: "75%"
            },
            body_composition: {
                weight: 75.2,
                body_fat: 12.5,
                muscle_mass: 65.8,
                trend: "improving"
            },
            performance_metrics: {
                squat_1rm: 120,
                bench_1rm: 90,
                deadlift_1rm: 140,
                total: 350
            }
        };
    }

    if (url.includes('/nutrition_data') || url.includes('/diet')) {
        return {
            daily_calories: 2200,
            macros: {
                protein: 150,
                carbs: 250,
                fat: 80,
                fiber: 35
            },
            meal_plan: [
                { meal: "早餐", calories: 450, protein: 25, carbs: 45, fat: 18 },
                { meal: "午餐", calories: 650, protein: 45, carbs: 75, fat: 22 },
                { meal: "晚餐", calories: 600, protein: 40, carbs: 65, fat: 20 },
                { meal: "加餐", calories: 500, protein: 40, carbs: 65, fat: 20 }
            ],
            recommendations: [
                "训练前1小时补充碳水化合物",
                "训练后30分钟内补充蛋白质",
                "每日饮水量建议2.5-3升",
                "睡前3小时避免大量进食"
            ],
            supplements: [
                { name: "乳清蛋白粉", timing: "训练后", amount: "30g" },
                { name: "肌酸", timing: "训练前", amount: "5g" },
                { name: "维生素D", timing: "随餐", amount: "1000IU" }
            ]
        };
    }

    if (url.includes('/expert_guidance') || url.includes('/coaching')) {
        return {
            tips: [
                {
                    title: "正确的深蹲技巧",
                    content: "保持核心收紧，膝盖与脚尖方向一致，下蹲至大腿平行地面",
                    category: "技巧指导"
                },
                {
                    title: "训练后恢复建议",
                    content: "充足睡眠、适当拉伸、补充营养是恢复的三大要素",
                    category: "恢复指导"
                }
            ],
            video_tutorials: [
                { title: "深蹲标准动作", duration: "3:45", url: "mock_video_1" },
                { title: "卧推安全技巧", duration: "4:20", url: "mock_video_2" }
            ],
            personalized_advice: "根据您的训练数据，建议增加下肢力量训练频率"
        };
    }

    // 默认返回通用的高级功能数据
    return {
        message: "高级功能已解锁",
        access_granted: true,
        premium_content: true,
        features_available: ["所有功能", "无限制访问", "专业指导", "高级统计"]
    };
}

// 重写POST函数
global.POST = async function(url, data, timeout = 15, errorCallback, successCallback) {
    console.log("拦截POST请求:", url, data);
    
    // 对于特定的付费验证接口，直接返回成功
    if (url.includes('/check_vip') || 
        url.includes('/verify_payment') || 
        url.includes('/subscription_status')) {
        const response = createSuccessResponse({
            vip: true,
            premium: true,
            subscription_active: true
        });
        successCallback && successCallback();
        return response;
    }
    
    // 对于用户信息接口，返回模拟数据
    if (url.includes('/get_userinfo')) {
        const response = createSuccessResponse(mockUserInfo);
        successCallback && successCallback();
        return response;
    }
    
    // 对于登录相关接口，返回成功
    if (url.includes('/login') || url.includes('/auth')) {
        const response = createSuccessResponse({
            token: "free_access_token_" + Date.now(),
            user: mockUserInfo
        });
        successCallback && successCallback();
        return response;
    }
    
    // 检查是否为高级功能接口
    if (PREMIUM_FEATURE_ENDPOINTS.some(endpoint => url.includes(endpoint))) {
        console.log("拦截高级功能接口:", url);
        const response = createSuccessResponse(generateMockPremiumData(url));
        successCallback && successCallback();
        return response;
    }

    // 其他请求尝试原始逻辑，但处理各种错误
    try {
        const result = await originalPOST(url, data, timeout, errorCallback, successCallback);

        // 如果收到jwt-error，不处理，直接返回成功
        if (result && result.msg === 'jwt-error') {
            console.log("忽略JWT错误，返回成功响应");
            return createSuccessResponse(data);
        }

        // 检查响应中是否有VIP限制，如果有则解除
        if (result && result.res) {
            if (result.res.vip_required === true) {
                console.log("解除VIP限制");
                result.res.vip_required = false;
                result.res.access_granted = true;
            }

            if (result.res.premium_only === true) {
                console.log("解除Premium限制");
                result.res.premium_only = false;
                result.res.free_access = true;
            }
        }

        return result;
    } catch (error) {
        console.log("网络请求失败，返回模拟成功响应:", error);

        // 如果是高级功能请求失败，提供离线数据
        if (PREMIUM_FEATURE_ENDPOINTS.some(endpoint => url.includes(endpoint))) {
            const response = createSuccessResponse(generateMockPremiumData(url));
            successCallback && successCallback();
            return response;
        }

        successCallback && successCallback();
        return createSuccessResponse(data);
    }
};

// 重写GET函数
global.GET = async function(url, debug) {
    console.log("拦截GET请求:", url);
    
    // 用户信息接口
    if (url.includes('/get_userinfo')) {
        return mockUserInfo;
    }
    
    // VIP状态检查
    if (url.includes('/vip_status') || url.includes('/subscription')) {
        return {
            vip: true,
            premium: true,
            features: ['all_features_unlocked']
        };
    }
    
    // 其他GET请求
    try {
        const result = await originalGET(url, debug);
        
        // 忽略jwt-error
        if (result && result.msg === 'jwt-error') {
            console.log("忽略JWT错误，返回默认数据");
            return {};
        }
        
        return result;
    } catch (error) {
        console.log("GET请求失败，返回空对象:", error);
        return {};
    }
};

// 重写其他网络函数
global.POST_GZIP = async function(url, data, timeout, errorCallback, successCallback) {
    console.log("拦截POST_GZIP请求:", url);
    
    try {
        const result = await originalPOST_GZIP(url, data, timeout, errorCallback, successCallback);
        
        if (result && result.msg === 'jwt-error') {
            console.log("忽略JWT错误");
            successCallback && successCallback();
            return createSuccessResponse(data);
        }
        
        return result;
    } catch (error) {
        console.log("POST_GZIP请求失败:", error);
        successCallback && successCallback();
        return createSuccessResponse(data);
    }
};

// 重写Food相关函数
global.Food_POST = async function(url, data, timeout, errorCallback, successCallback) {
    console.log("拦截Food_POST请求:", url);
    
    try {
        const result = await originalFood_POST(url, data, timeout, errorCallback, successCallback);
        
        if (result && result.msg === 'jwt-error') {
            console.log("忽略Food JWT错误");
            successCallback && successCallback();
            return data; // Food_POST返回res而不是完整响应
        }
        
        return result;
    } catch (error) {
        console.log("Food_POST请求失败:", error);
        successCallback && successCallback();
        return data;
    }
};

global.Food_GET = async function(url, debug) {
    console.log("拦截Food_GET请求:", url);
    
    try {
        const result = await originalFood_GET(url, debug);
        
        if (result && result.msg === 'jwt-error') {
            console.log("忽略Food JWT错误");
            return {};
        }
        
        return result;
    } catch (error) {
        console.log("Food_GET请求失败:", error);
        return {};
    }
};

// 禁用token清理函数
global.removeScanCode = async function() {
    console.log("禁用removeScanCode函数");
    return Promise.resolve();
};

// 设置免费token
global.token = "free_access_token_permanent";

console.log("训记应用免费化补丁已加载");
