// 训记应用免费化补丁 - 本地存储修改
// 这个文件包含了修改本地存储逻辑的代码

import AsyncStorage from '@react-native-async-storage/async-storage';

// 保存原始的AsyncStorage方法
const originalGetItem = AsyncStorage.getItem;
const originalSetItem = AsyncStorage.setItem;
const originalRemoveItem = AsyncStorage.removeItem;

// VIP相关的存储键
const VIP_KEYS = [
    'vip_status',
    'subscription_status', 
    'premium_features',
    'payment_status',
    'user_level'
];

// 免费用户的默认设置
const FREE_USER_SETTINGS = {
    'vip_status': 'true',
    'subscription_status': 'active',
    'premium_features': 'true',
    'payment_status': 'paid',
    'user_level': 'premium',
    'token': 'free_access_token_permanent',
    'nickname': '免费用户',
    'avatar': 'https://tupian.xunjiapp.cn/default_avatar.png'
};

// 重写getItem方法
AsyncStorage.getItem = async function(key) {
    console.log("拦截AsyncStorage.getItem:", key);
    
    // 如果是VIP相关的键，返回免费设置
    if (VIP_KEYS.includes(key) || FREE_USER_SETTINGS[key]) {
        const value = FREE_USER_SETTINGS[key];
        console.log(`返回免费设置 ${key}: ${value}`);
        return Promise.resolve(value);
    }
    
    // 对于token相关的键，确保返回有效token
    if (key === 'token' || key === 'fake_token') {
        return Promise.resolve('free_access_token_permanent');
    }
    
    // 其他键使用原始方法
    try {
        return await originalGetItem(key);
    } catch (error) {
        console.log(`AsyncStorage.getItem失败 ${key}:`, error);
        return null;
    }
};

// 重写setItem方法
AsyncStorage.setItem = async function(key, value) {
    console.log("拦截AsyncStorage.setItem:", key, value);
    
    // 阻止清除VIP状态
    if (VIP_KEYS.includes(key) && (value === 'false' || value === null || value === '')) {
        console.log(`阻止清除VIP设置: ${key}`);
        return Promise.resolve();
    }
    
    // 阻止清除token
    if ((key === 'token' || key === 'fake_token') && (!value || value === '')) {
        console.log(`阻止清除token: ${key}`);
        return Promise.resolve();
    }
    
    // 其他设置正常保存
    try {
        return await originalSetItem(key, value);
    } catch (error) {
        console.log(`AsyncStorage.setItem失败 ${key}:`, error);
        return Promise.resolve();
    }
};

// 重写removeItem方法
AsyncStorage.removeItem = async function(key) {
    console.log("拦截AsyncStorage.removeItem:", key);
    
    // 阻止删除VIP相关设置和token
    if (VIP_KEYS.includes(key) || key === 'token' || key === 'fake_token' || key === 'isQrcodeLogin_v2') {
        console.log(`阻止删除关键设置: ${key}`);
        return Promise.resolve();
    }
    
    // 其他键正常删除
    try {
        return await originalRemoveItem(key);
    } catch (error) {
        console.log(`AsyncStorage.removeItem失败 ${key}:`, error);
        return Promise.resolve();
    }
};

// 初始化免费用户设置
async function initializeFreeUserSettings() {
    console.log("初始化免费用户设置");
    
    for (const [key, value] of Object.entries(FREE_USER_SETTINGS)) {
        try {
            await originalSetItem(key, value);
            console.log(`设置 ${key}: ${value}`);
        } catch (error) {
            console.log(`设置失败 ${key}:`, error);
        }
    }
}

// 应用启动时初始化
setTimeout(initializeFreeUserSettings, 1000);

console.log("训记应用存储补丁已加载");
